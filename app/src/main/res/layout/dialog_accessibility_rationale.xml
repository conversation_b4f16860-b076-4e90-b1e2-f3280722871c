<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerInParent="false">
    
    <RelativeLayout
        android:background="@drawable/white_block"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:elevation="25dp">
        
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            
            <!-- Title Section -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                
                <!-- Close Button -->
                <RelativeLayout
                    android:id="@+id/close_button_container"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true">
                    
                    <Button
                        android:id="@+id/close_button"
                        android:background="@drawable/grey_block_line_up"
                        android:layout_width="45sp"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="5dp"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_centerInParent="true"
                        android:src="@drawable/ic_close"
                        android:contentDescription="@string/accessibility_rationale_close"
                        android:scaleType="centerInside"/>
                </RelativeLayout>
                
                <!-- Title Text -->
                <TextView
                    android:id="@+id/title_text"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_toStartOf="@id/close_button_container"
                    android:layout_alignParentStart="true"
                    android:text="@string/accessibility_service_usage_title"
                    android:textAlignment="center"
                    android:gravity="center"/>
            </RelativeLayout>
            
            <!-- Body Content -->
            <LinearLayout
                android:orientation="vertical"
                android:background="@drawable/grey_block"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:layout_marginBottom="8dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp">
                
                <TextView
                    android:id="@+id/message_text"
                    android:textSize="16sp"
                    android:textColor="?attr/black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/accessibility_service_usage_message"
                    android:textAlignment="viewStart"
                    android:lineSpacingExtra="4dp"/>
            </LinearLayout>
            
            <!-- Checkbox Section -->
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:gravity="center_vertical">
                
                <CheckBox
                    android:id="@+id/agreement_checkbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:buttonTint="?attr/colorr"/>
                
                <TextView
                    android:id="@+id/agreement_text"
                    android:textSize="16sp"
                    android:textColor="?attr/black"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/i_agree"/>
            </LinearLayout>
            
            <!-- Button Section -->
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:gravity="end">
                
                <!-- Close Text Button -->
                <Button
                    android:id="@+id/close_text_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="@string/accessibility_rationale_close"
                    android:textColor="?attr/unselected_button"
                    android:background="@android:color/transparent"
                    android:textSize="16sp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    style="@style/Widget.AppCompat.Button.Borderless"/>
                
                <!-- Next Contained Button -->
                <Button
                    android:id="@+id/next_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/accessibility_rationale_next"
                    android:textColor="@android:color/white"
                    android:background="@drawable/grey_block_line_up"
                    android:textSize="16sp"
                    android:paddingStart="24dp"
                    android:paddingEnd="24dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:enabled="false"/>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
